/**
 * 🚀 ENHANCED CUSTOMER 360° DASHBOARD 2025 - COSMIC LEVEL UX
 *
 * <PERSON><PERSON><PERSON><PERSON><PERSON> profil klienta w Europie - oszczędza czas szefa HVAC:
 * - Real-time profile updates z AI insights
 * - Email & call interaction timeline
 * - Predictive analytics & recommendations
 * - Business owner actionable insights
 * - Time-saving automation alerts
 * - Cosmic-level design z golden ratio
 *
 * Features:
 * - Interactive customer profile visualization
 * - Real-time sentiment tracking
 * - Predictive maintenance alerts
 * - Upsell opportunity detection
 * - Communication style adaptation
 * - Mobile-responsive cosmic design
 * - AI-powered business intelligence
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Row,
  Col,
  Avatar,
  Tag,
  Progress,
  Timeline,
  Statistic,
  Alert,
  Button,
  Tabs,
  Space,
  Divider,
  Badge,
  Tooltip,
  Rate,
  List,
  Typography,
  Spin,
  notification,
  Modal,
  Drawer
} from 'antd';
import {
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  HomeOutlined,
  ToolOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  HeartOutlined,
  AlertOutlined,
  TrendingUpOutlined,
  BulbOutlined,
  CalendarOutlined,
  MessageOutlined,
  SettingOutlined,
  ReloadOutlined,
  ExportOutlined,
  StarOutlined,
  <PERSON>boltOutlined,
  EyeOutlined,
  EditOutlined
} from '@ant-design/icons';
import axios from 'axios';
import { CosmicCard, CosmicSummaryCard } from '../cosmic';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

const EnhancedCustomer360Dashboard = ({
  customerId,
  onClose,
  visible = true,
  mode = 'modal' // 'modal' | 'drawer' | 'page'
}) => {
  const [loading, setLoading] = useState(true);
  const [customer360, setCustomer360] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [realTimeUpdates, setRealTimeUpdates] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);

  /**
   * 📊 Load Customer 360° data with error handling
   */
  const loadCustomer360Data = useCallback(async () => {
    if (!customerId) return;

    try {
      setLoading(true);
      setError(null);

      console.log(`🎯 Loading Customer 360° Profile for: ${customerId}`);

      const response = await axios.get(`/api/intelligence/customer/${customerId}/360`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        setCustomer360(response.data.data);

        // Show notifications for urgent alerts
        if (response.data.data.predictiveInsights?.nextBestActions?.length > 0) {
          const urgentActions = response.data.data.predictiveInsights.nextBestActions
            .filter(action => action.priority === 'high');

          urgentActions.forEach(action => {
            notification.warning({
              message: '⚡ Pilne Działanie!',
              description: action.action,
              duration: 10,
              placement: 'topRight'
            });
          });
        }
      } else {
        throw new Error(response.data.message || 'Failed to load customer data');
      }

    } catch (error) {
      console.error('❌ Failed to load customer 360° data:', error);
      setError(error.message);
      notification.error({
        message: '❌ Błąd Ładowania',
        description: 'Nie udało się załadować profilu klienta 360°',
        duration: 5
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [customerId]);

  // Load customer 360° data on mount and setup real-time updates
  useEffect(() => {
    if (customerId && visible) {
      loadCustomer360Data();

      // Setup real-time updates
      if (realTimeUpdates) {
        const interval = setInterval(loadCustomer360Data, 30000); // Update every 30 seconds
        return () => clearInterval(interval);
      }
    }
  }, [customerId, visible, realTimeUpdates, loadCustomer360Data]);

  /**
   * 🔄 Manual refresh function
   */
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadCustomer360Data();
  }, [loadCustomer360Data]);

  /**
   * 📤 Export customer data
   */
  const handleExport = useCallback(() => {
    if (!customer360) return;

    const exportData = {
      customerId,
      exportDate: new Date().toISOString(),
      customerProfile: customer360
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

    const exportFileDefaultName = `customer-360-${customerId}-${new Date().toISOString().split('T')[0]}.json`;

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();

    notification.success({
      message: '📤 Eksport Zakończony',
      description: 'Profil klienta został wyeksportowany',
      duration: 3
    });
  }, [customer360, customerId]);

  /**
   * 🎨 Get sentiment color
   */
  const getSentimentColor = useCallback((sentiment) => {
    const colors = {
      very_positive: '#52c41a',
      positive: '#73d13d',
      neutral: '#faad14',
      negative: '#ff7875',
      very_negative: '#ff4d4f'
    };
    return colors[sentiment] || '#d9d9d9';
  }, []);

  /**
   * 🏷️ Get risk level tag
   */
  const getRiskTag = useCallback((riskScore) => {
    if (riskScore > 0.7) return <Tag color="red">Wysokie Ryzyko</Tag>;
    if (riskScore > 0.4) return <Tag color="orange">Średnie Ryzyko</Tag>;
    return <Tag color="green">Niskie Ryzyko</Tag>;
  }, []);

  /**
   * 💰 Format currency
   */
  const formatCurrency = useCallback((amount) => {
    return new Intl.NumberFormat('pl-PL', {
      style: 'currency',
      currency: 'PLN'
    }).format(amount);
  }, []);

  /**
   * 📊 Get technical knowledge percentage
   */
  const getTechnicalKnowledgePercent = useCallback((level) => {
    const levels = { basic: 25, intermediate: 50, advanced: 75, expert: 100 };
    return levels[level] || 25;
  }, []);

  /**
   * 🔧 Get interaction icon
   */
  const getInteractionIcon = useCallback((type) => {
    const icons = {
      email: <MailOutlined />,
      call: <PhoneOutlined />,
      service: <ToolOutlined />,
      visit: <HomeOutlined />
    };
    return icons[type] || <MessageOutlined />;
  }, []);

  // Loading state with cosmic design
  if (loading) {
    return (
      <CosmicCard className="animate-fade-in" style={{ textAlign: 'center', padding: 'var(--space-xxl)' }}>
        <div className="animate-scale-in">
          <Spin size="large" />
          <Title level={4} style={{ marginTop: 'var(--space-md)', color: 'var(--hvac-primary)' }}>
            🚀 Ładowanie Profilu Klienta 360°
          </Title>
          <Text type="secondary">
            Analizujemy wszystkie dane klienta z AI insights...
          </Text>
        </div>
      </CosmicCard>
    );
  }

  // Error state with cosmic design
  if (error || !customer360) {
    return (
      <CosmicCard status="error" className="animate-fade-in">
        <Alert
          message="❌ Błąd Ładowania Profilu"
          description={error || "Nie udało się załadować profilu klienta 360°"}
          type="error"
          showIcon
          action={
            <Space>
              <Button
                size="small"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={refreshing}
              >
                Spróbuj Ponownie
              </Button>
              {onClose && (
                <Button size="small" onClick={onClose}>
                  Zamknij
                </Button>
              )}
            </Space>
          }
        />
      </CosmicCard>
    );
  }

  const { basicInfo, communicationProfile, technicalProfile, businessProfile,
          behavioralPatterns, predictiveInsights, interactionHistory } = customer360;

  // Main content wrapper
  const contentStyle = {
    padding: 'var(--space-lg)',
    background: 'var(--hvac-gray-50)',
    minHeight: mode === 'page' ? '100vh' : 'auto'
  };

  return (
    <div style={contentStyle} className="animate-fade-in">
      {/* 🎯 Enhanced Header with Cosmic Design */}
      <CosmicCard
        className="animate-slide-in"
        style={{ marginBottom: 'var(--space-lg)' }}
        gradient={true}
      >
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: 'var(--space-md)'
        }}>
          {/* Customer Info Section */}
          <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-md)' }}>
            <Avatar
              size={80}
              icon={<UserOutlined />}
              style={{
                backgroundColor: 'var(--hvac-primary)',
                border: '3px solid var(--hvac-white)',
                boxShadow: 'var(--shadow-md)'
              }}
            />
            <div>
              <Title level={2} style={{ margin: 0, color: 'var(--hvac-primary)' }}>
                ⭐ {basicInfo?.name || 'Nieznany Klient'}
              </Title>
              <Text type="secondary" style={{ fontSize: 'var(--font-size-md)' }}>
                🏢 {basicInfo?.buildingType} • 📍 {basicInfo?.address}
              </Text>
              <div style={{ marginTop: 'var(--space-sm)' }}>
                <Space wrap>
                  <Badge
                    status={realTimeUpdates ? "processing" : "default"}
                    text={realTimeUpdates ? "Live Updates" : "Static View"}
                  />
                  {getRiskTag(predictiveInsights?.churnRisk?.score || 0)}
                  <Tag color="blue" icon={<DollarOutlined />}>
                    {businessProfile?.budgetIndicator || 'Standard'}
                  </Tag>
                  <Tag color="green" icon={<StarOutlined />}>
                    LTV: {formatCurrency(businessProfile?.lifetimeValue || 0)}
                  </Tag>
                </Space>
              </div>
            </div>
          </div>

          {/* Action Buttons Section */}
          <div>
            <Space wrap>
              <Tooltip title="Odśwież dane">
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={refreshing}
                  className="cosmic-btn"
                />
              </Tooltip>
              <Tooltip title="Eksportuj profil">
                <Button
                  icon={<ExportOutlined />}
                  onClick={handleExport}
                  className="cosmic-btn"
                />
              </Tooltip>
              <Button
                type="primary"
                icon={<MailOutlined />}
                onClick={() => window.open(`mailto:${basicInfo?.email}`)}
                className="cosmic-btn-primary"
              >
                📧 Email
              </Button>
              <Button
                icon={<PhoneOutlined />}
                onClick={() => window.open(`tel:${basicInfo?.phone}`)}
                className="cosmic-btn-secondary"
              >
                📞 Zadzwoń
              </Button>
              {onClose && (
                <Button onClick={onClose} className="cosmic-btn">
                  ✖️ Zamknij
                </Button>
              )}
            </Space>
          </div>
        </div>
      </CosmicCard>

      {/* 📊 Enhanced Quick Stats with Cosmic Design */}
      <Row gutter={[16, 16]} style={{ marginBottom: 'var(--space-lg)' }} className="animate-fade-in">
        <Col xs={24} sm={6}>
          <CosmicSummaryCard
            hoverable
            className="animate-scale-in"
            style={{ animationDelay: '0.1s' }}
          >
            <Statistic
              title="💖 Satysfakcja Klienta"
              value={behavioralPatterns?.loyaltyIndicators?.satisfaction || 4.2}
              suffix="/ 5.0"
              prefix={<HeartOutlined style={{ color: 'var(--hvac-success)' }} />}
              precision={1}
              valueStyle={{ color: 'var(--hvac-success)', fontSize: 'var(--font-size-xl)' }}
            />
            <div style={{ marginTop: 'var(--space-sm)' }}>
              <Rate
                disabled
                value={behavioralPatterns?.loyaltyIndicators?.satisfaction || 4.2}
                style={{ fontSize: '12px' }}
              />
            </div>
          </CosmicSummaryCard>
        </Col>
        <Col xs={24} sm={6}>
          <CosmicSummaryCard
            hoverable
            className="animate-scale-in"
            style={{ animationDelay: '0.2s' }}
          >
            <Statistic
              title="💰 Wartość LTV"
              value={businessProfile?.lifetimeValue || 15000}
              prefix={<DollarOutlined style={{ color: 'var(--hvac-primary)' }} />}
              formatter={(value) => formatCurrency(value)}
              valueStyle={{ color: 'var(--hvac-primary)', fontSize: 'var(--font-size-xl)' }}
            />
            <div style={{ marginTop: 'var(--space-sm)' }}>
              <Progress
                percent={Math.min((businessProfile?.lifetimeValue || 0) / 50000 * 100, 100)}
                strokeColor="var(--hvac-primary)"
                size="small"
                showInfo={false}
              />
            </div>
          </CosmicSummaryCard>
        </Col>
        <Col xs={24} sm={6}>
          <CosmicSummaryCard
            hoverable
            className="animate-scale-in"
            style={{ animationDelay: '0.3s' }}
          >
            <Statistic
              title="💬 Interakcje"
              value={interactionHistory?.totalInteractions || 0}
              prefix={<MessageOutlined style={{ color: 'var(--hvac-secondary)' }} />}
              valueStyle={{ color: 'var(--hvac-secondary)', fontSize: 'var(--font-size-xl)' }}
            />
            <div style={{ marginTop: 'var(--space-sm)' }}>
              <Space>
                <Tag color="blue">{interactionHistory?.interactionTypes?.emails || 0} 📧</Tag>
                <Tag color="green">{interactionHistory?.interactionTypes?.calls || 0} 📞</Tag>
              </Space>
            </div>
          </CosmicSummaryCard>
        </Col>
        <Col xs={24} sm={6}>
          <CosmicSummaryCard
            hoverable
            className="animate-scale-in"
            style={{ animationDelay: '0.4s' }}
          >
            <Statistic
              title="⏰ Ostatni Kontakt"
              value={interactionHistory?.lastInteraction ?
                new Date(interactionHistory.lastInteraction).toLocaleDateString('pl-PL') : 'Brak'}
              prefix={<ClockCircleOutlined style={{ color: 'var(--hvac-warning)' }} />}
              valueStyle={{ color: 'var(--hvac-warning)', fontSize: 'var(--font-size-md)' }}
            />
            <div style={{ marginTop: 'var(--space-sm)' }}>
              <Text type="secondary" style={{ fontSize: 'var(--font-size-sm)' }}>
                {interactionHistory?.lastInteraction ?
                  `${Math.floor((Date.now() - new Date(interactionHistory.lastInteraction)) / (1000 * 60 * 60 * 24))} dni temu` :
                  'Brak kontaktu'
                }
              </Text>
            </div>
          </CosmicSummaryCard>
        </Col>
      </Row>

      {/* 🚨 Enhanced Urgent Alerts with Cosmic Design */}
      {predictiveInsights?.nextBestActions?.filter(action => action.priority === 'high').length > 0 && (
        <CosmicCard
          status="warning"
          className="animate-fade-in"
          style={{ marginBottom: 'var(--space-lg)' }}
        >
          <Alert
            message={
              <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}>
                <ThunderboltOutlined style={{ color: 'var(--hvac-warning)', fontSize: 'var(--font-size-lg)' }} />
                <Text strong style={{ color: 'var(--hvac-warning)', fontSize: 'var(--font-size-md)' }}>
                  ⚡ Pilne Działania Wymagane dla Szefa!
                </Text>
              </div>
            }
            description={
              <List
                size="small"
                dataSource={predictiveInsights.nextBestActions.filter(action => action.priority === 'high')}
                renderItem={(action, index) => (
                  <List.Item
                    className="animate-slide-in"
                    style={{
                      animationDelay: `${index * 0.1}s`,
                      padding: 'var(--space-sm)',
                      borderRadius: 'var(--radius-base)',
                      background: 'rgba(250, 173, 20, 0.05)',
                      marginBottom: 'var(--space-xs)'
                    }}
                    actions={[
                      <Button
                        type="primary"
                        size="small"
                        icon={<EyeOutlined />}
                        style={{ backgroundColor: 'var(--hvac-warning)' }}
                      >
                        Wykonaj
                      </Button>
                    ]}
                  >
                    <List.Item.Meta
                      avatar={<AlertOutlined style={{ color: 'var(--hvac-warning)' }} />}
                      title={
                        <Text strong style={{ color: 'var(--hvac-gray-800)' }}>
                          🎯 {action.action}
                        </Text>
                      }
                      description={
                        <Space direction="vertical" size="small">
                          <Text type="secondary">⏰ {action.timing}</Text>
                          <Text style={{ color: 'var(--hvac-success)' }}>
                            📈 Oczekiwany rezultat: {action.expectedOutcome}
                          </Text>
                        </Space>
                      }
                    />
                  </List.Item>
                )}
              />
            }
            type="warning"
            showIcon
            style={{ border: 'none', background: 'transparent' }}
          />
        </CosmicCard>
      )}

      {/* 📋 Enhanced Tabs with Cosmic Design */}
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        size="large"
        className="animate-fade-in"
        style={{
          background: 'var(--hvac-white)',
          borderRadius: 'var(--radius-lg)',
          padding: 'var(--space-md)',
          boxShadow: 'var(--shadow-md)'
        }}
      >
        {/* 👤 Overview Tab - Enhanced */}
        <TabPane
          tab={
            <span style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-xs)' }}>
              <UserOutlined />
              👤 Przegląd Klienta
            </span>
          }
          key="overview"
        >
          <Row gutter={[16, 16]} className="animate-fade-in">
            <Col xs={24} lg={12}>
              <CosmicCard
                title={
                  <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}>
                    <MessageOutlined style={{ color: 'var(--hvac-primary)' }} />
                    📞 Profil Komunikacji
                  </div>
                }
                extra={<Badge status="processing" text="Live AI Analysis" />}
                hoverable
                className="animate-scale-in"
              >
                <Space direction="vertical" style={{ width: '100%' }} size="middle">
                  <div style={{
                    padding: 'var(--space-sm)',
                    background: 'var(--hvac-gray-50)',
                    borderRadius: 'var(--radius-base)'
                  }}>
                    <Text strong style={{ color: 'var(--hvac-gray-700)' }}>📡 Preferowany Kanał:</Text>
                    <Tag
                      color="blue"
                      style={{ marginLeft: 'var(--space-sm)', fontSize: 'var(--font-size-sm)' }}
                      icon={communicationProfile?.preferredChannel === 'email' ? <MailOutlined /> : <PhoneOutlined />}
                    >
                      {communicationProfile?.preferredChannel || 'email'}
                    </Tag>
                  </div>

                  <div style={{
                    padding: 'var(--space-sm)',
                    background: 'var(--hvac-gray-50)',
                    borderRadius: 'var(--radius-base)'
                  }}>
                    <Text strong style={{ color: 'var(--hvac-gray-700)' }}>🎭 Styl Komunikacji:</Text>
                    <Tag
                      color="green"
                      style={{ marginLeft: 'var(--space-sm)', fontSize: 'var(--font-size-sm)' }}
                    >
                      {communicationProfile?.communicationStyle || 'professional'}
                    </Tag>
                  </div>

                  <div style={{
                    padding: 'var(--space-sm)',
                    background: 'var(--hvac-gray-50)',
                    borderRadius: 'var(--radius-base)'
                  }}>
                    <Text strong style={{ color: 'var(--hvac-gray-700)' }}>📈 Trend Sentymentu:</Text>
                    <div style={{ marginTop: 'var(--space-sm)' }}>
                      {communicationProfile?.sentimentTrend?.slice(-5).map((sentiment, index) => (
                        <Tag
                          key={index}
                          style={{
                            marginBottom: 'var(--space-xs)',
                            marginRight: 'var(--space-xs)',
                            backgroundColor: getSentimentColor(sentiment.sentiment),
                            color: 'white',
                            border: 'none'
                          }}
                        >
                          {sentiment.sentiment} ({Math.round(sentiment.confidence * 100)}%)
                        </Tag>
                      )) || (
                        <Text type="secondary">Brak danych o sentymencie</Text>
                      )}
                    </div>
                  </div>
                </Space>
              </CosmicCard>
            </Col>
            
            <Col xs={24} lg={12}>
              <CosmicCard
                title={
                  <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}>
                    <ToolOutlined style={{ color: 'var(--hvac-secondary)' }} />
                    🔧 Profil Techniczny
                  </div>
                }
                extra={<Badge count="HVAC Expert" style={{ backgroundColor: 'var(--hvac-secondary)' }} />}
                hoverable
                className="animate-scale-in"
                style={{ animationDelay: '0.1s' }}
              >
                <Space direction="vertical" style={{ width: '100%' }} size="middle">
                  <div style={{
                    padding: 'var(--space-sm)',
                    background: 'var(--hvac-gray-50)',
                    borderRadius: 'var(--radius-base)'
                  }}>
                    <Text strong style={{ color: 'var(--hvac-gray-700)' }}>🧠 Wiedza Techniczna:</Text>
                    <div style={{ marginTop: 'var(--space-sm)' }}>
                      <Progress
                        percent={getTechnicalKnowledgePercent(technicalProfile?.technicalKnowledge)}
                        strokeColor="var(--hvac-primary)"
                        trailColor="var(--hvac-gray-200)"
                        style={{ width: '100%' }}
                        format={(percent) => `${technicalProfile?.technicalKnowledge || 'basic'} (${percent}%)`}
                      />
                    </div>
                  </div>

                  <div style={{
                    padding: 'var(--space-sm)',
                    background: 'var(--hvac-gray-50)',
                    borderRadius: 'var(--radius-base)'
                  }}>
                    <Text strong style={{ color: 'var(--hvac-gray-700)' }}>🏭 Preferowane Marki:</Text>
                    <div style={{ marginTop: 'var(--space-sm)' }}>
                      {technicalProfile?.preferredBrands?.map(brand => (
                        <Tag
                          key={brand}
                          color="blue"
                          style={{
                            marginBottom: 'var(--space-xs)',
                            marginRight: 'var(--space-xs)',
                            fontSize: 'var(--font-size-sm)'
                          }}
                        >
                          {brand}
                        </Tag>
                      )) || (
                        <Text type="secondary">Brak preferencji marek</Text>
                      )}
                    </div>
                  </div>

                  <div style={{
                    padding: 'var(--space-sm)',
                    background: 'var(--hvac-gray-50)',
                    borderRadius: 'var(--radius-base)'
                  }}>
                    <Text strong style={{ color: 'var(--hvac-gray-700)' }}>⚙️ Świadomość Serwisu:</Text>
                    <Tag
                      color={technicalProfile?.maintenanceAwareness === 'proactive' ? 'green' : 'orange'}
                      style={{
                        marginLeft: 'var(--space-sm)',
                        fontSize: 'var(--font-size-sm)'
                      }}
                      icon={technicalProfile?.maintenanceAwareness === 'proactive' ? <StarOutlined /> : <ClockCircleOutlined />}
                    >
                      {technicalProfile?.maintenanceAwareness || 'reactive'}
                    </Tag>
                  </div>
                </Space>
              </CosmicCard>
            </Col>
          </Row>

          <Row gutter={[16, 16]} style={{ marginTop: 'var(--space-md)' }}>
            <Col xs={24}>
              <CosmicCard
                title={
                  <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}>
                    <TrendingUpOutlined style={{ color: 'var(--hvac-success)' }} />
                    💰 Możliwości Biznesowe dla Szefa
                  </div>
                }
                extra={
                  <Badge
                    count={predictiveInsights?.upsellOpportunities?.length || 0}
                    style={{ backgroundColor: 'var(--hvac-success)' }}
                  />
                }
                hoverable
                className="animate-fade-in"
              >
                <Row gutter={[16, 16]}>
                  {predictiveInsights?.upsellOpportunities?.map((opportunity, index) => (
                    <Col xs={24} sm={12} lg={8} key={index}>
                      <CosmicCard
                        size="small"
                        status="success"
                        hoverable
                        className="animate-scale-in"
                        style={{
                          animationDelay: `${index * 0.1}s`,
                          height: '100%'
                        }}
                      >
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          height: '100%'
                        }}>
                          <div style={{ flex: 1 }}>
                            <Title level={5} style={{
                              margin: 0,
                              color: 'var(--hvac-gray-800)',
                              marginBottom: 'var(--space-xs)'
                            }}>
                              🎯 {opportunity.product}
                            </Title>
                            <div style={{ marginBottom: 'var(--space-xs)' }}>
                              <Text type="secondary" style={{ fontSize: 'var(--font-size-sm)' }}>
                                📊 Prawdopodobieństwo: {Math.round(opportunity.probability * 100)}%
                              </Text>
                            </div>
                            <div style={{ marginBottom: 'var(--space-xs)' }}>
                              <Text strong style={{
                                color: 'var(--hvac-success)',
                                fontSize: 'var(--font-size-md)'
                              }}>
                                💵 {formatCurrency(opportunity.value)}
                              </Text>
                            </div>
                            <div>
                              <Text type="secondary" style={{ fontSize: 'var(--font-size-xs)' }}>
                                ⏰ {opportunity.timing}
                              </Text>
                            </div>
                          </div>
                          <div style={{ marginLeft: 'var(--space-sm)' }}>
                            <Progress
                              type="circle"
                              percent={Math.round(opportunity.probability * 100)}
                              width={60}
                              strokeColor="var(--hvac-success)"
                              trailColor="var(--hvac-gray-200)"
                              format={(percent) => (
                                <span style={{ fontSize: '10px', color: 'var(--hvac-success)' }}>
                                  {percent}%
                                </span>
                              )}
                            />
                          </div>
                        </div>
                      </CosmicCard>
                    </Col>
                  )) || (
                    <Col xs={24}>
                      <div style={{
                        textAlign: 'center',
                        padding: 'var(--space-lg)',
                        color: 'var(--hvac-gray-500)'
                      }}>
                        <BulbOutlined style={{ fontSize: 'var(--font-size-xxl)', marginBottom: 'var(--space-sm)' }} />
                        <div>Brak zidentyfikowanych możliwości biznesowych</div>
                        <Text type="secondary">AI analizuje dane klienta...</Text>
                      </div>
                    </Col>
                  )}
                </Row>
              </CosmicCard>
            </Col>
          </Row>
        </TabPane>

        {/* 📞 Enhanced Interaction History Tab */}
        <TabPane
          tab={
            <span style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-xs)' }}>
              <MessageOutlined />
              📞 Historia Interakcji
            </span>
          }
          key="interactions"
        >
          <CosmicCard
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}>
                <ClockCircleOutlined style={{ color: 'var(--hvac-primary)' }} />
                📅 Timeline Interakcji z Klientem
              </div>
            }
            extra={
              <Space>
                <Badge
                  count={interactionHistory?.totalInteractions || 0}
                  style={{ backgroundColor: 'var(--hvac-primary)' }}
                />
                <Button
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={refreshing}
                >
                  Odśwież
                </Button>
              </Space>
            }
            className="animate-fade-in"
          >
            <Timeline mode="left" style={{ marginTop: 'var(--space-md)' }}>
              {interactionHistory?.recentInteractions?.map((interaction, index) => (
                <Timeline.Item
                  key={index}
                  color={getSentimentColor(interaction.sentiment)}
                  dot={
                    <div style={{
                      background: getSentimentColor(interaction.sentiment),
                      borderRadius: '50%',
                      padding: 'var(--space-xs)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      {getInteractionIcon(interaction.type)}
                    </div>
                  }
                  className="animate-slide-in"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <CosmicCard
                    size="small"
                    hoverable
                    style={{
                      marginBottom: 'var(--space-sm)',
                      borderLeft: `4px solid ${getSentimentColor(interaction.sentiment)}`
                    }}
                  >
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <div style={{ flex: 1 }}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)', marginBottom: 'var(--space-xs)' }}>
                          <Text strong style={{ color: 'var(--hvac-primary)', fontSize: 'var(--font-size-md)' }}>
                            {interaction.type.toUpperCase()}
                          </Text>
                          <Tag
                            color={getSentimentColor(interaction.sentiment)}
                            style={{ color: 'white', border: 'none' }}
                          >
                            {interaction.sentiment}
                          </Tag>
                        </div>

                        <div style={{
                          color: 'var(--hvac-gray-600)',
                          fontSize: 'var(--font-size-sm)',
                          marginBottom: 'var(--space-sm)'
                        }}>
                          📅 {new Date(interaction.date).toLocaleString('pl-PL')}
                        </div>

                        <Paragraph
                          ellipsis={{ rows: 2, expandable: true }}
                          style={{ marginBottom: 'var(--space-sm)' }}
                        >
                          {interaction.summary}
                        </Paragraph>

                        <Space wrap>
                          <Tag icon={<StarOutlined />} color="blue">
                            {interaction.outcome}
                          </Tag>
                          {interaction.duration && (
                            <Tag icon={<ClockCircleOutlined />} color="green">
                              {interaction.duration}
                            </Tag>
                          )}
                          {interaction.priority && (
                            <Tag
                              color={interaction.priority === 'high' ? 'red' : 'orange'}
                              icon={<AlertOutlined />}
                            >
                              {interaction.priority}
                            </Tag>
                          )}
                        </Space>
                      </div>
                    </div>
                  </CosmicCard>
                </Timeline.Item>
              )) || (
                <Timeline.Item dot={<MessageOutlined />}>
                  <div style={{
                    textAlign: 'center',
                    padding: 'var(--space-lg)',
                    color: 'var(--hvac-gray-500)'
                  }}>
                    <MessageOutlined style={{ fontSize: 'var(--font-size-xxl)', marginBottom: 'var(--space-sm)' }} />
                    <div>Brak historii interakcji</div>
                    <Text type="secondary">Interakcje będą wyświetlane tutaj</Text>
                  </div>
                </Timeline.Item>
              )}
            </Timeline>
          </CosmicCard>
        </TabPane>

        {/* 🧠 Enhanced Predictive Analytics Tab - KLUCZOWE DLA SZEFA */}
        <TabPane
          tab={
            <span style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-xs)' }}>
              <BulbOutlined />
              🧠 AI Analityka Predykcyjna
            </span>
          }
          key="analytics"
        >
          <Row gutter={[16, 16]} className="animate-fade-in">
            <Col xs={24} lg={12}>
              <CosmicCard
                title={
                  <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}>
                    <AlertOutlined style={{ color: 'var(--hvac-error)' }} />
                    ⚠️ Ryzyko Utraty Klienta
                  </div>
                }
                extra={getRiskTag(predictiveInsights?.churnRisk?.score || 0)}
                hoverable
                status={predictiveInsights?.churnRisk?.score > 0.5 ? 'error' : 'success'}
                className="animate-scale-in"
              >
                <div style={{ marginBottom: 'var(--space-md)' }}>
                  <Progress
                    percent={Math.round((predictiveInsights?.churnRisk?.score || 0) * 100)}
                    strokeColor={predictiveInsights?.churnRisk?.score > 0.5 ? 'var(--hvac-error)' : 'var(--hvac-success)'}
                    trailColor="var(--hvac-gray-200)"
                    strokeWidth={8}
                    format={(percent) => (
                      <span style={{
                        color: predictiveInsights?.churnRisk?.score > 0.5 ? 'var(--hvac-error)' : 'var(--hvac-success)',
                        fontWeight: 'bold'
                      }}>
                        {percent}%
                      </span>
                    )}
                  />
                </div>

                <div style={{
                  padding: 'var(--space-sm)',
                  background: 'var(--hvac-gray-50)',
                  borderRadius: 'var(--radius-base)',
                  marginBottom: 'var(--space-md)'
                }}>
                  <Text strong style={{ color: 'var(--hvac-gray-700)' }}>🎯 Czynniki Ryzyka:</Text>
                  <List
                    size="small"
                    dataSource={predictiveInsights?.churnRisk?.factors || ['Brak danych o czynnikach ryzyka']}
                    renderItem={(factor, index) => (
                      <List.Item
                        style={{
                          padding: 'var(--space-xs) 0',
                          borderBottom: index < (predictiveInsights?.churnRisk?.factors?.length || 1) - 1 ? '1px solid var(--hvac-gray-200)' : 'none'
                        }}
                      >
                        <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-xs)' }}>
                          <AlertOutlined style={{ color: 'var(--hvac-warning)', fontSize: 'var(--font-size-sm)' }} />
                          <Text>{factor}</Text>
                        </div>
                      </List.Item>
                    )}
                  />
                </div>

                <Divider style={{ margin: 'var(--space-md) 0' }} />

                <div style={{
                  padding: 'var(--space-sm)',
                  background: predictiveInsights?.churnRisk?.score > 0.5 ? 'rgba(255, 77, 79, 0.05)' : 'rgba(82, 196, 26, 0.05)',
                  borderRadius: 'var(--radius-base)',
                  border: `1px solid ${predictiveInsights?.churnRisk?.score > 0.5 ? 'var(--hvac-error)' : 'var(--hvac-success)'}`
                }}>
                  <Text strong style={{ color: 'var(--hvac-gray-700)' }}>💡 Rekomendacja dla Szefa:</Text>
                  <Paragraph style={{
                    marginTop: 'var(--space-xs)',
                    marginBottom: 0,
                    color: 'var(--hvac-gray-600)'
                  }}>
                    {predictiveInsights?.churnRisk?.recommendation || 'AI analizuje dane klienta i przygotowuje rekomendacje...'}
                  </Paragraph>
                </div>
              </CosmicCard>
            </Col>
            
            <Col xs={24} lg={12}>
              <Card title="Potrzeby Serwisowe" extra={<SettingOutlined />}>
                <List
                  dataSource={predictiveInsights?.maintenanceNeeds || []}
                  renderItem={need => (
                    <List.Item>
                      <List.Item.Meta
                        avatar={
                          <Badge 
                            status={need.urgency === 'immediate' ? 'error' : 'processing'} 
                          />
                        }
                        title={need.equipment}
                        description={
                          <div>
                            <div>{need.description}</div>
                            <div style={{ marginTop: 4 }}>
                              <Text strong>{formatCurrency(need.estimatedCost)}</Text>
                              <Text type="secondary" style={{ marginLeft: 8 }}>
                                • {need.urgency}
                              </Text>
                            </div>
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* Next Actions Tab */}
        <TabPane 
          tab={
            <span>
              <CalendarOutlined />
              Następne Kroki
            </span>
          } 
          key="actions"
        >
          <Card title="Rekomendowane Działania" extra={<BulbOutlined />}>
            <List
              dataSource={predictiveInsights?.nextBestActions || []}
              renderItem={action => (
                <List.Item
                  actions={[
                    <Button 
                      type={action.priority === 'high' ? 'primary' : 'default'}
                      size="small"
                    >
                      Wykonaj
                    </Button>
                  ]}
                >
                  <List.Item.Meta
                    avatar={
                      <Badge 
                        status={
                          action.priority === 'high' ? 'error' : 
                          action.priority === 'medium' ? 'warning' : 'default'
                        } 
                      />
                    }
                    title={
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <Text strong>{action.action}</Text>
                        <Tag color={
                          action.priority === 'high' ? 'red' : 
                          action.priority === 'medium' ? 'orange' : 'blue'
                        }>
                          {action.priority}
                        </Tag>
                      </div>
                    }
                    description={
                      <div>
                        <div><Text strong>Timing:</Text> {action.timing}</div>
                        <div><Text strong>Oczekiwany Rezultat:</Text> {action.expectedOutcome}</div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );

  // Helper methods
  getTechnicalKnowledgePercent(level) {
    const levels = { basic: 25, intermediate: 50, advanced: 75, expert: 100 };
    return levels[level] || 25;
  }

  getInteractionIcon(type) {
    const icons = {
      email: <MailOutlined />,
      call: <PhoneOutlined />,
      service: <ToolOutlined />,
      visit: <HomeOutlined />
    };
    return icons[type] || <MessageOutlined />;
  }
};

export default EnhancedCustomer360Dashboard;
